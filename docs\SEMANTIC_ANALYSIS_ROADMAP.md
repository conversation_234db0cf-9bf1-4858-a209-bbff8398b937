# Baa Language Semantic Analysis Implementation Roadmap (New Design)

**Status: This document outlines the implementation plan for the new Semantic Analysis phase. All items are planned unless otherwise noted. This phase will operate on the AST generated by the new <PERSON><PERSON><PERSON> and will align with the design in `docs/SEMANTIC_ANALYSIS.md`.**

## Phase 1: Foundation - Symbol Tables and Basic Name Resolution

- **Symbol Table Core:**
  - [ ] Design and implement basic symbol table structure (e.g., per scope, using hash tables).
  - [ ] Implement functions for adding symbols (variables, functions) to a scope.
    - [ ] Initial symbol attributes: type, kind, definition location.
  - [ ] Implement functions for looking up symbols in the current and enclosing scopes.
- **Scope Management:**
  - [ ] Implement mechanisms to create/enter new scopes (global, function, block).
  - [ ] Implement mechanisms to exit/destroy scopes.
- **Basic Name Resolution:**
  - [ ] Traverse AST for variable declarations and add them to the symbol table.
  - [ ] Traverse AST for variable usage (`BAA_EXPR_VARIABLE`) and resolve them to their declarations in the symbol table.
  - [ ] Report errors for undeclared identifiers.
- **AST Annotation (Initial):**
  - [ ] Annotate `BAA_EXPR_VARIABLE` nodes with a pointer to their symbol table entry.

## Phase 2: Basic Type Checking

- **Literal Type Checking:**
  - [ ] Assign types to literal nodes in the AST (e.g., `123` is `عدد_صحيح`, `"نص"` is `حرف[]` or a string type, `صحيح` is `منطقي`).
- **Variable Declaration Type Checking:**
  - [ ] If an initializer is present, check its type compatibility with the declared variable type.
- **Expression Type Checking (Simple Cases):**
  - [ ] Determine and annotate the type of simple unary and binary expressions involving literals and resolved variables.
  - [ ] Use `baa_validate_unary_op` and `baa_validate_binary_op` from `src/operators/` for basic validation.
- **Assignment Type Checking:**
  - [ ] Check type compatibility for simple assignment (`=`).
- **Constant Expression Evaluation (Basic):**
  - [ ] Implement basic compile-time constant expression evaluation (e.g., for simple arithmetic involving integer literals used in `ثابت` initializers or array sizes if supported early).

## Phase 3: Control Flow and Function Semantics

- **Function Declaration Semantics:**
  - [ ] Add function symbols to the symbol table (including parameter types and return type).
  - [ ] Process parameter types and names, adding them to the function's scope.
- **Function Call Semantics:**
  - [ ] Resolve function names using the symbol table.
  - [ ] Check arity (number of arguments).
  - [ ] Check argument types against parameter types (initial support for exact matches).
  - [ ] Determine and annotate the type of the function call expression (based on function return type).
- **Return Statement Type Checking:**
  - [ ] Check the type of the returned expression against the current function's declared return type.
  - [ ] Ensure `إرجع` without a value is only in void functions.
  - [ ] Ensure non-void functions have `إرجع` with a value on at least one path (basic check).
- **Control Flow Analysis Integration (from `src/analysis/flow_analysis.c`):**
  - [ ] Adapt existing `flow_analysis.c` to traverse the new AST.
  - [ ] Perform checks for `توقف` (break) and `استمر` (continue) statement validity within loops/switches.
- **Conditional Expression Type Checking:**
  - [ ] Ensure conditions in `إذا`, `طالما`, `لكل` are boolean or convertible to boolean.

## Phase 4: Advanced Type System and Semantic Rules

- **Array Type Semantics:**
  - [ ] Type checking for array indexing (`BAA_EXPR_INDEX`).
  - [ ] Type checking for array literal elements (`BAA_EXPR_ARRAY`).
- **Cast Expression Semantics (`BAA_EXPR_CAST`):**
  - [ ] Validate explicit type casts based on defined language rules.
- **Compound Assignment Semantics:**
  - [ ] Type checking for compound assignment operators (e.g., `+=`, `-=`).
- **`ثابت` (const) Correctness:**
  - [ ] Ensure `ثابت` variables are initialized at declaration.
  - [ ] Report errors for attempts to modify `ثابت` variables.
- **`مقيد` (restrict) and `مضمن` (inline) Semantics:**
  - [ ] Implement any semantic checks related to `مقيد` (e.g., applied to correct pointer types).
  - [ ] Implement any semantic checks for `مضمن` (if any, beyond being a hint).
- **Full Type Compatibility and Conversion Rules:**
  - [ ] Implement comprehensive rules for implicit and explicit type conversions as defined in the language specification.
- **AST Annotation (Semantic-driven Transformations):**
  - [ ] Implement insertion of explicit cast nodes (`BAA_EXPR_CAST`) in the AST for valid implicit conversions.

## Phase 5: Advanced Static Analysis & Semantic Checks

- **Enhanced Control Flow Analysis:**
    - [ ] Implement more precise "all paths return" analysis for non-void functions using graph-based algorithms.
    - [ ] Implement Definite Assignment Analysis (ensuring variables are assigned before use on all paths).
    - [ ] Implement more comprehensive Dead Code Detection.
- **Richer Symbol Information:**
    - [ ] Extend symbol table entries and analysis to track `is_initialized` and `is_used` for variables, providing warnings.
- **Attribute/Annotation Processing (if language feature is added):**
    - [ ] Semantic validation and processing for language-defined attributes (e.g., `@deprecated`).
- **Optional Linter-like Checks:**
    - [ ] Implement warnings for common pitfalls like variable shadowing.

## Future Considerations (Longer Term)

- **User-Defined Types:**
  - [ ] Semantic analysis for `بنية` (struct) definitions: member scopes, type checking for member access (`::`, `->`).
  - [ ] Semantic analysis for `اتحاد` (union) definitions and member access.
  - [ ] Semantic analysis for `تعداد` (enum) definitions and usage.
  - [ ] Rules for user-defined type equivalence (name vs. structural).
- **Pointers:**
  - [ ] Semantic analysis for pointer types, dereferencing, address-of operator.
- **Advanced Function Features:**
  - [ ] Semantic analysis for optional parameters with default values.
  - [ ] Semantic analysis for rest parameters.
  - [ ] Semantic analysis for named arguments in function calls.
  - [ ] Support for Function/Operator Overloading in symbol tables and name resolution (if feature is added).
- **Module System:**
  - [ ] Symbol table and name resolution enhancements for a Module/Namespace system (if feature is added).
- **Type Inference:**
  - [ ] Explore more advanced (but still constrained) type inference beyond simple initializer-based inference.
- **Semantic AST Transformations:**
  - [ ] Other forms of desugaring or AST canonicalization based on semantic information.

## Error Reporting and AST Annotation (Ongoing Refinement)

- [ ] Continuously improve error messages for clarity, context, and precision, with correct source locations.
  - [ ] Implement mechanisms to suppress cascading semantic errors.
  - [ ] Provide "Did you mean?" suggestions for misspelled identifiers/types.
- [ ] Ensure all relevant AST nodes are comprehensively annotated with necessary semantic information (resolved types, symbol links, lvalue/rvalue status, constant values, etc.).

## Testing Strategy

- [ ] Develop unit tests for individual semantic checks (e.g., type compatibility function, symbol lookup, scope management).
- [ ] Create test Baa programs with various correct and incorrect semantic constructs to cover all implemented rules.
- [ ] Verify that the semantic analyzer correctly annotates the AST for valid programs.
- [ ] Verify that all expected semantic errors are caught and reported correctly (with accurate locations) for invalid programs.
