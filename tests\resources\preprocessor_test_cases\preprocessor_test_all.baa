// ==============================================
// ===        ملف اختبار المعالج المسبق       ===
// ==============================================
// الإصدار المستهدف: 0.1.27.0

// --- 0. الإعداد الأولي والتعليقات ---
// هذا تعليق سطر واحد
// هذا تعليق
// متعدد الأسطر.
// ملاحظة: التعليقات متعددة الأسطر (/* */) غير مدعومة حالياً في المعالج المسبق
#تعريف VERSION "0.1.27.0" // تعليق في نهاية السطر لتحديد الإصدار

// --- 1. اختب<PERSON>ر #تضمين (File Inclusion) ---
// هذا الاختبار يتطلب وجود الملفين "include_test_header.baa" و "nested_include.baa"
// في نفس المسار أو في مسارات التضمين المحددة.
#تضمين "include_test_header.baa"
// السطر التالي يجب أن يأتي من "include_test_header.baa":
// سطر من header_test.baa

#تعريف SHOULD_INCLUDE_NESTED
#إذا_عرف SHOULD_INCLUDE_NESTED
  #تضمين "nested_include.baa"
  // رسالة من تضمين متداخل.
#نهاية_إذا

// --- 2. اختبار #تعريف و #الغاء_تعريف (Macro Definition & Undefinition) ---
#تعريف PI 3.14
#تعريف GREETING "مرحباً يا عالم"
#تعريف CONST_INT 123
#تعريف EMPTY_MACRO // تعريف فارغ

قيمة PI هي PI.
التحية: GREETING.
الرقم: CONST_INT.
فارغ: EMPTY_MACRO نهاية.

#الغاء_تعريف CONST_INT
#إذا_عرف CONST_INT
  خطأ في الاختبار: CONST_INT لا يزال معرفًا!
#إلا
  نجاح: تم إلغاء تعريف CONST_INT بنجاح.
#نهاية_إذا

// اختبار إعادة التعريف (يجب أن يحل الجديد محل القديم)
#تعريف PI 3.14159
قيمة PI الجديدة هي PI.

// --- 3. اختبار الماكروهات الوظيفية (Function-like Macros) ---
#تعريف SQUARE(x) ((x)*(x))
#تعريف ADD(a, b) (a + b)
#تعريف MULTIPLY(x, y) x * y
#تعريف COMPLEX_FN_MACRO(a,b,c) (SQUARE(a) + ADD(b,c))

مربع 5 هو SQUARE(5).           // Expected: ((5)*(5)) -> 25
جمع 3 و 4 هو ADD(3, 4).         // Expected: (3 + 4) -> 7
ضرب 2 و 3 هو MULTIPLY(2, 3).   // Expected: 2 * 3 -> 6
مربع (ADD(1,2)) هو SQUARE(ADD(1,2)). // Expected: (((1 + 2))*((1 + 2))) -> 9
ماكرو مركب: COMPLEX_FN_MACRO(2,3,4). // Expected: (((2)*(2)) + (3 + 4)) -> 4 + 7 -> 11

// --- 4. اختبار # (Stringification) ---
#تعريف STRINGIFY(val) #val
#تعريف STRINGIFY_EXPR(expr) "التعبير " #expr " قيمته " STRINGIFY(expr)

نص_محول: STRINGIFY(مثال).            // Expected: "مثال"
نص_رقم_محول: STRINGIFY(123).         // Expected: "123"
نص_تعبير_محول: STRINGIFY(10 + 20).  // Expected: "10 + 20"
رسالة_تعبير: STRINGIFY_EXPR(ADD(5,5)). // Expected: "التعبير " "ADD(5,5)" " قيمته " "ADD(5,5)"

// --- 5. اختبار ## (Token Pasting) ---
#تعريف CONCAT(a, b) a##b
#تعريف MAKE_VAR(type, name) type CONCAT(var_, name)
#تعريف PASTE_EMPTY(a,b) a##b
#تعريف PASTE_WITH_NUM(prefix, num) prefix##num

MAKE_VAR(عدد_صحيح, counter) = 10. // Expected: عدد_صحيح var_counter = 10.
عدد_صحيح CONCAT(val, 1) = 1.      // Expected: عدد_صحيح val1 = 1.
عدد_صحيح CONCAT(val, CONCAT(2, _suffix)) = 2. // Expected: عدد_صحيح val2_suffix = 2.
رسالة_لصق PASTE_EMPTY(hello,world). // Expected: helloworld
رسالة_لصق_فارغ_يمين PASTE_EMPTY(hello,). // Expected: hello
رسالة_لصق_فارغ_يسار PASTE_EMPTY(,world). // Expected: world
عدد_صحيح PASTE_WITH_NUM(counter, 123) = 0. // Expected: عدد_صحيح counter123 = 0.

// --- 6. اختبار الماكروهات المتغيرة (Variadic Macros) ---
#تعريف DEBUG_LOG(format, وسائط_إضافية) print_debug(VERSION, format, __وسائط_متغيرة__)
#تعريف SIMPLE_LOG(وسائط_إضافية) print_simple(__وسائط_متغيرة__)
#تعريف MIN_ARGS(a, وسائط_إضافية) first_arg_is_a then __وسائط_متغيرة__

DEBUG_LOG("قيمة س = %d", س). // Expected: print_debug("0.1.27.0", "قيمة س = %d", س)
DEBUG_LOG("نقطة تتبع").       // Expected: print_debug("0.1.27.0", "نقطة تتبع", )
SIMPLE_LOG(1, 2, "ثلاثة").    // Expected: print_simple(1, 2, "ثلاثة")
SIMPLE_LOG().                // Expected: print_simple() - __وسائط_متغيرة__ is empty
MIN_ARGS(فقط_أ).            // Expected: first_arg_is_a then

// --- 7. اختبار إعادة فحص الماكرو (Rescanning) ---
#تعريف X Y
#تعريف Y Z
#تعريف Z النهاية_لإعادة_الفحص
إعادة_فحص_بسيط: X // Expected: النهاية_لإعادة_الفحص

#تعريف A B C_Suffix
#تعريف B VAL_B
#تعريف C_Suffix VAL_C_Suffix
إعادة_فحص_مركب: A // Expected: VAL_B VAL_C_Suffix

#تعريف LOOP1 LOOP2
#تعريف LOOP2 LOOP1
// LOOP1 // هذا يسبب تكرارًا لا نهائيًا، المعالج يجب أن يكتشفه ويتوقف.
// تم التعليق مؤقتاً لاختبار الأخطاء الأخرى أولاً.
تجاوز اختبار التكرار المباشر للماكرو.

#تعريف PASTE_RESCAN_LHS lhs_val
#تعريف PASTE_RESCAN_RHS rhs_val
#تعريف PASTE_THEN_RESCAN PASTE_RESCAN_LHS ## _ ## PASTE_RESCAN_RHS
// سلوك C القياسي: يتم توسيع الوسائط ما لم تكن جزءًا من # أو ##.
// ثم يتم إعادة فحص النتيجة الكاملة.
لصق_مع_إعادة_فحص: PASTE_THEN_RESCAN // Expected: lhs_val_rhs_val

#تعريف WRAP(content) START content END
#تعريف INNER INNER_VAL
تغليف_مع_إعادة_فحص: WRAP(INNER) // Expected: START INNER_VAL END

// اختبار معقد لإعادة الفحص مع اللصق (من المثال الأصلي)
#تعريف PREFIX pre
#تعريف SUFFIX post
#تعريف COMBINE(p,s) p ## s
#تعريف APPLY_PREFIX(val) COMBINE(PREFIX, val)
#تعريف APPLY_SUFFIX(val) COMBINE(val, SUFFIX)
#تعريف FULL_NAME APPLY_PREFIX(APPLY_SUFFIX(Name))
// وفقًا لمعيار C، وسيطات ## لا يتم توسيعها مبدئيًا.
// APPLY_PREFIX(APPLY_SUFFIX(Name)) -> COMBINE(PREFIX, APPLY_SUFFIX(Name))
// -> pre##APPLY_SUFFIX(Name) -> preAPPLY_SUFFIX(Name)
// Rescan: preAPPLY_SUFFIX(Name) -> preCOMBINE(Name, SUFFIX)
// -> pre(Name##SUFFIX) -> preNamepost
الاسم_الكامل_لصق_معقد: FULL_NAME. // Expected: preNamepost

// --- 8. اختبار التجميع الشرطي (Conditional Compilation) ---
#تعريف FEATURE_A 1
#تعريف FEATURE_B 0
// UNDEFINED_MACRO غير معرف

#إذا FEATURE_A
  ميزة أ مفعلة (صحيح).
#نهاية_إذا

#إذا FEATURE_B
  خطأ في الاختبار: ميزة ب يجب ألا تكون مفعلة.
#إلا
  ميزة ب غير مفعلة (صحيح).
#نهاية_إذا

#إذا معرف(FEATURE_A) && !معرف(FEATURE_C)
  تعبير مركب مع "معرف" صحيح.
#نهاية_إذا

// --- اختبار معرف مع وسيط ماكرو (الاختبار الحاسم لعدم التوسيع) ---
#تعريف ACTUAL_TARGET_MACRO real_value
#تعريف POINTER_MACRO ACTUAL_TARGET_MACRO

// معرف POINTER_MACRO should check if POINTER_MACRO is defined (it is). It should NOT expand POINTER_MACRO to ACTUAL_TARGET_MACRO first.
#إذا معرف POINTER_MACRO
  نجح_معرف_مؤشر: POINTER_MACRO معرف (لم يتم توسيعه).
#إلا
  فشل: POINTER_MACRO يجب أن يكون معرف.
#نهاية_إذا

// معرف ACTUAL_TARGET_MACRO should check if ACTUAL_TARGET_MACRO is defined (it is).
#إذا معرف ACTUAL_TARGET_MACRO
  نجح_معرف_هدف: ACTUAL_TARGET_MACRO معرف.
#إلا
  فشل: ACTUAL_TARGET_MACRO يجب أن يكون معرف.
#نهاية_إذا

#تعريف UNPOINTED_MACRO some_other_value
// معرف POINTER_TO_UNDEFINED should check if POINTER_TO_UNDEFINED is defined (it's not).
#إذا معرف POINTER_TO_UNDEFINED_MACRO
  فشل: POINTER_TO_UNDEFINED_MACRO لا يجب أن يكون معرف.
#إلا
  نجح_معرف_غير_معرف: POINTER_TO_UNDEFINED_MACRO غير معرف.
#نهاية_إذا

// اختبار معرف معرف (فحص ما إذا كان "معرف" معرف كماكرو)
#إذا معرف معرف
  فشل: معرف لا يجب أن يكون معرف كماكرو بشكل افتراضي.
#إلا
  نجح_معرف_معرف: معرف ليس ماكرو (صحيح).
#نهاية_إذا

// تعريف "معرف" كماكرو، ثم اختبار
#تعريف معرف test_value
#إذا معرف معرف
  نجح_معرف_كماكرو: معرف الآن معرف كماكرو.
#إلا
  فشل: معرف يجب أن يكون معرف كماكرو الآن.
#نهاية_إذا
#الغاء_تعريف معرف

// اختبار حفظ المسافات البيضاء
#تعريف SPACED_MACRO
#إذا معرف    SPACED_MACRO
  نجح_مسافات: معرف مع مسافات يعمل.
#نهاية_إذا
#إذا معرف (   SPACED_MACRO   )
  نجح_مسافات_أقواس: معرف مع مسافات وأقواس يعمل.
#نهاية_إذا

// اختبار تعبير مركب مع معرف
#تعريف FLAG1 1
#تعريف FLAG2 1
#إذا معرف FLAG1 && معرف FLAG2 && FLAG1 + FLAG2 == 2
  نجح_تعبير_مركب: معرف في تعبير مركب يعمل.
#نهاية_إذا

// اختبار أن التوسيع العادي لا يزال يعمل
#إذا POINTER_MACRO == real_value  // هذا يجب أن يوسع POINTER_MACRO
  نجح_توسيع_عادي: التوسيع العادي للماكرو لا يزال يعمل.
#نهاية_إذا

#إذا FEATURE_A == 1
  FEATURE_A قيمتها 1 (صحيح).
#نهاية_إذا

#إذا FEATURE_B != 0
  خطأ في الاختبار: FEATURE_B يجب أن تكون 0.
#نهاية_إذا

#إذا UNDEFINED_MACRO // تقييم إلى 0 في التعبير الشرطي
  خطأ في الاختبار: UNDEFINED_MACRO لا يجب أن يعتبر صحيحًا.
#إلا
  UNDEFINED_MACRO تقييم إلى خطأ (صحيح).
#نهاية_إذا

// اختبار #وإلا_إذا
#تعريف LEVEL 2
#إذا LEVEL == 1
  المستوى 1 (خطأ).
#وإلا_إذا LEVEL == 2 && معرف(FEATURE_A)
  المستوى 2 وهو الصحيح.
#وإلا_إذا LEVEL == 3
  المستوى 3 (خطأ).
#إلا
  مستوى آخر (خطأ).
#نهاية_إذا

// اختبار عوامل البت والأسبقية
#تعريف FLAGS 0x05 // Binary: ...0101
#إذا (FLAGS & 0x01) && (FLAGS | 0x02) == 0x07 && (FLAGS ^ 0x04) == 0x01 && (~FLAGS & 0x0F) == 0x0A && (FLAGS << 1) == 0x0A && (FLAGS >> 1) == 0x02
  عوامل البت تعمل بشكل صحيح.
#إلا
  خطأ في الاختبار: خطأ في عوامل البت.
#نهاية_إذا

// اختبار الأعداد السداسية عشرية والثنائية في التعابير الشرطية
#تعريف HEX_VAL 0x10
#تعريف BIN_VAL 0b101
#إذا HEX_VAL == 16 && BIN_VAL == 5
  الأعداد السداسية عشرية والثنائية تعمل في #إذا.
#إلا
  خطأ في الاختبار: الأعداد السداسية عشرية/الثنائية في #إذا.
#نهاية_إذا

// --- 9. اختبار الماكروهات المدمجة (Predefined Macros) ---
// هذه القيم تعتمد على مسار الملف ووقت التشغيل.
// سنركز على أنها تتوسع إلى شيء.
الملف الحالي: __الملف__
السطر الحالي: __السطر__
التاريخ الحالي: __التاريخ__
الوقت الحالي: __الوقت__
الدالة الحالية (مكان مؤقت): __الدالة__
إصدار باء: __إصدار_المعيار_باء__ // Expected: 10150L

سطر آخر لتغيير __السطر__.
السطر الجديد: __السطر__.

// --- 10. اختبار #تحذير (Warning Directive) ---
#تحذير هذه رسالة تحذير اختبارية. // يجب أن يظهر هذا التحذير في stderr.
هذا السطر يجب أن يظهر في stdout بعد التحذير.

#إذا 1 // كتلة شرطية صحيحة
  #تحذير تحذير داخل كتلة #إذا.
  سطر داخل كتلة شرطية صحيحة.
#نهاية_إذا

#إذا 0 // كتلة شرطية خاطئة
  #تحذير هذا التحذير يجب ألا يظهر.
  هذا السطر يجب ألا يظهر.
#نهاية_إذا

#تحذير رسالة تحذير مع // تعليق في نهايتها

// --- 11. اختبار تقييم التعبير المتقدم في #إذا (Advanced Expression Evaluation in #if) ---
// اختبار توسيع الماكرو قبل التقييم
#تعريف OP_PLUS +
#تعريف VAL1 10
#تعريف VAL2 20
#إذا VAL1 OP_PLUS VAL2 == 30
  توسيع_الماكرو_في_العملية: OP_PLUS_WORKS
#إلا
  خطأ_في_توسيع_الماكرو_في_العملية
#نهاية_إذا

#تعريف IS_ZERO(x) ((x) == 0)
#تعريف ADD_FOR_IF(a,b) a+b
#إذا IS_ZERO(ADD_FOR_IF(5, -5)) // يتطلب توسيع ماكرو وظيفي
  توسيع_الماكرو_الوظيفي_في_الشرط: FUNC_MACRO_IN_IF_WORKS
#إلا
  خطأ_في_توسيع_الماكرو_الوظيفي_في_الشرط
#نهاية_إذا

#تعريف GET_VAL VAL_A_IF
#تعريف VAL_A_IF 5
#إذا GET_VAL > 0 // يتطلب إعادة فحص GET_VAL ثم VAL_A_IF
  إعادة_فحص_الماكرو_في_الشرط: RESCAN_IN_IF_WORKS
#إلا
  خطأ_في_إعادة_فحص_الماكرو_في_الشرط
#نهاية_إذا

// --- 11.5. اختبار شامل للماكروهات الوظيفية في التعابير الشرطية ---
// هذا الاختبار يتحقق من تطبيق الميزة الجديدة: Full Macro Expansion in Conditional Expressions

// اختبار أساسي: ماكرو وظيفي بسيط في #إذا
#تعريف IS_GREATER(a,b) ((a) > (b))
#تعريف MAX_SIZE 100
#تعريف CURRENT_SIZE 50
#إذا IS_GREATER(MAX_SIZE, CURRENT_SIZE)
  نجح_ماكرو_وظيفي_بسيط: IS_GREATER يعمل في #إذا
#إلا
  فشل_ماكرو_وظيفي_بسيط: IS_GREATER لا يعمل في #إذا
#نهاية_إذا

// اختبار ماكروهات وظيفية متداخلة (بدون ternary operator حتى يتم تطبيقه)
#تعريف SIMPLE_ADD(a,b) ((a) + (b))
#تعريف SIMPLE_MULT(a,b) ((a) * (b))
#تعريف NESTED_CALC(x,y,z) SIMPLE_ADD(SIMPLE_MULT(x, y), z)
#إذا NESTED_CALC(5, 3, 2) == 17
  نجح_ماكرو_متداخل: ماكروهات وظيفية متداخلة تعمل في #إذا
#إلا
  فشل_ماكرو_متداخل: ماكروهات وظيفية متداخلة لا تعمل في #إذا
#نهاية_إذا

// اختبار إعادة فحص مع ماكروهات وظيفية
#تعريف GET_BASE() BASE_VALUE_IF
#تعريف BASE_VALUE_IF 42
#تعريف IS_EQUAL(x,y) ((x) == (y))
#إذا IS_EQUAL(GET_BASE(), 42)
  نجح_إعادة_فحص_وظيفي: إعادة فحص مع ماكروهات وظيفية يعمل
#إلا
  فشل_إعادة_فحص_وظيفي: إعادة فحص مع ماكروهات وظيفية لا يعمل
#نهاية_إذا

// اختبار ماكروهات متغيرة في التعابير الشرطية
#تعريف SUM_THREE(a,b,c) ((a) + (b) + (c))
#تعريف CHECK_SUM(expected, وسائط_إضافية) (SUM_THREE(__وسائط_متغيرة__) == (expected))
#إذا CHECK_SUM(15, 3, 5, 7)
  نجح_ماكرو_متغير_شرطي: ماكروهات متغيرة تعمل في #إذا
#إلا
  فشل_ماكرو_متغير_شرطي: ماكروهات متغيرة لا تعمل في #إذا
#نهاية_إذا

// اختبار مزيج من معرف() والماكروهات الوظيفية
#تعريف HAS_FEATURE(feature) معرف(feature)
#تعريف ENABLED_FEATURE some_value
#إذا HAS_FEATURE(ENABLED_FEATURE) && IS_GREATER(MAX_SIZE, 0)
  نجح_مزيج_معرف_وظيفي: مزيج معرف() والماكروهات الوظيفية يعمل
#إلا
  فشل_مزيج_معرف_وظيفي: مزيج معرف() والماكروهات الوظيفية لا يعمل
#نهاية_إذا

// اختبار ماكروهات وظيفية مع #وإلا_إذا
#تعريف CATEGORY_A 1
#تعريف CATEGORY_B 2
#تعريف CATEGORY_C 3
#تعريف CURRENT_CATEGORY CATEGORY_B
#تعريف IS_CATEGORY(cat) (CURRENT_CATEGORY == (cat))
#إذا IS_CATEGORY(CATEGORY_A)
  فشل: يجب ألا يكون CATEGORY_A
#وإلا_إذا IS_CATEGORY(CATEGORY_B)
  نجح_وإلا_إذا_وظيفي: ماكروهات وظيفية تعمل في #وإلا_إذا
#وإلا_إذا IS_CATEGORY(CATEGORY_C)
  فشل: يجب ألا يكون CATEGORY_C
#إلا
  فشل: لم يتم تحديد فئة صحيحة
#نهاية_إذا

// اختبار ماكروهات وظيفية معقدة مع عمليات حسابية
#تعريف SQUARE(x) ((x) * (x))
#تعريف POWER_OF_TWO(exp) (1 << (exp))
#تعريف IS_POWER_OF_TWO(n) (((n) & ((n) - 1)) == 0 && (n) > 0)
#إذا IS_POWER_OF_TWO(POWER_OF_TWO(3)) && SQUARE(4) == 16
  نجح_ماكرو_حسابي_معقد: ماكروهات وظيفية معقدة تعمل في #إذا
#إلا
  فشل_ماكرو_حسابي_معقد: ماكروهات وظيفية معقدة لا تعمل في #إذا
#نهاية_إذا

// اختبار أن معرف() لا يزال يعمل بشكل صحيح (regression test)
#تعريف TEST_DEFINED_MACRO some_value
#إذا معرف(TEST_DEFINED_MACRO) && !معرف(UNDEFINED_TEST_MACRO)
  نجح_معرف_بعد_التحديث: معرف() لا يزال يعمل بعد تحديث الماكروهات الوظيفية
#إلا
  فشل_معرف_بعد_التحديث: معرف() لا يعمل بعد التحديث
#نهاية_إذا

// --- 11.6. اختبار العامل الثلاثي (Ternary Operator) في التعابير الشرطية ---
// هذا الاختبار يتحقق من تطبيق ميزة العامل الثلاثي: condition ? true_expr : false_expr

// اختبار أساسي: عامل ثلاثي بسيط
#تعريف DEBUG 1
#إذا DEBUG ? 1 : 0
  نجح_عامل_ثلاثي_بسيط: العامل الثلاثي البسيط يعمل
#إلا
  فشل_عامل_ثلاثي_بسيط: العامل الثلاثي البسيط لا يعمل
#نهاية_إذا

// اختبار مع قيمة خاطئة
#تعريف DISABLED 0
#إذا DISABLED ? 1 : 0
  فشل_عامل_ثلاثي_خاطئ: يجب أن يقيم إلى 0
#إلا
  نجح_عامل_ثلاثي_خاطئ: العامل الثلاثي مع شرط خاطئ يعمل
#نهاية_إذا

// اختبار العامل الثلاثي المتداخل (right-associative)
#تعريف LEVEL 2
#إذا LEVEL == 1 ? 10 : LEVEL == 2 ? 20 : 30
  نجح_عامل_ثلاثي_متداخل: العامل الثلاثي المتداخل يعمل (20)
#إلا
  فشل_عامل_ثلاثي_متداخل: العامل الثلاثي المتداخل لا يعمل
#نهاية_إذا

// اختبار العامل الثلاثي مع تعابير معقدة
#تعريف VERSION 3
#إذا (VERSION > 2) ? (VERSION < 5 ? 1 : 0) : 0
  نجح_عامل_ثلاثي_معقد: العامل الثلاثي مع تعابير معقدة يعمل
#إلا
  فشل_عامل_ثلاثي_معقد: العامل الثلاثي مع تعابير معقدة لا يعمل
#نهاية_إذا

// اختبار العامل الثلاثي مع الماكروهات
#تعريف FEATURE_ENABLED 1
#تعريف FALLBACK_VALUE 42
#تعريف PRIMARY_VALUE 99
#إذا FEATURE_ENABLED ? PRIMARY_VALUE : FALLBACK_VALUE
  نجح_عامل_ثلاثي_ماكرو: العامل الثلاثي مع الماكروهات يعمل (99)
#إلا
  فشل_عامل_ثلاثي_ماكرو: العامل الثلاثي مع الماكروهات لا يعمل
#نهاية_إذا

// اختبار العامل الثلاثي مع معرف()
#تعريف OPTIONAL_FEATURE some_value
#إذا معرف(OPTIONAL_FEATURE) ? 1 : 0
  نجح_عامل_ثلاثي_معرف: العامل الثلاثي مع معرف() يعمل
#إلا
  فشل_عامل_ثلاثي_معرف: العامل الثلاثي مع معرف() لا يعمل
#نهاية_إذا

// اختبار العامل الثلاثي مع عمليات حسابية
#تعريف BASE_VALUE 10
#إذا BASE_VALUE > 5 ? BASE_VALUE * 2 : BASE_VALUE / 2
  نجح_عامل_ثلاثي_حساب: العامل الثلاثي مع عمليات حسابية يعمل (20)
#إلا
  فشل_عامل_ثلاثي_حساب: العامل الثلاثي مع عمليات حسابية لا يعمل
#نهاية_إذا

// اختبار أسبقية العامل الثلاثي (أقل من العوامل المنطقية)
#تعريف FLAG1 1
#تعريف FLAG2 0
#إذا FLAG1 && FLAG2 ? 100 : FLAG1 || FLAG2 ? 200 : 300
  نجح_أسبقية_ثلاثي: أسبقية العامل الثلاثي صحيحة (200)
#إلا
  فشل_أسبقية_ثلاثي: أسبقية العامل الثلاثي خاطئة
#نهاية_إذا

// اختبار العامل الثلاثي مع الأقواس
#إذا (DEBUG ? 1 : 0) && (FEATURE_ENABLED ? 1 : 0)
  نجح_عامل_ثلاثي_أقواس: العامل الثلاثي مع الأقواس يعمل
#إلا
  فشل_عامل_ثلاثي_أقواس: العامل الثلاثي مع الأقواس لا يعمل
#نهاية_إذا

// اختبار العامل الثلاثي المتعدد المستويات
#تعريف MODE 2
#إذا MODE == 1 ? 1 : MODE == 2 ? MODE == 3 ? 3 : 2 : 0
  نجح_عامل_ثلاثي_متعدد: العامل الثلاثي متعدد المستويات يعمل (2)
#إلا
  فشل_عامل_ثلاثي_متعدد: العامل الثلاثي متعدد المستويات لا يعمل
#نهاية_إذا

// اختبار العامل الثلاثي مع قيم سالبة
#تعريف NEGATIVE_CHECK -5
#إذا NEGATIVE_CHECK < 0 ? 5 : NEGATIVE_CHECK
  نجح_عامل_ثلاثي_سالب: العامل الثلاثي مع قيم سالبة يعمل (5)
#إلا
  فشل_عامل_ثلاثي_سالب: العامل الثلاثي مع قيم سالبة لا يعمل
#نهاية_إذا

// --- 12. اختبار دقة موقع الخطأ في تعابير #إذا و #وإلا_إذا ---
// هذه الاختبارات يجب إلغاء تعليقها بشكل فردي للتحقق من موقع الخطأ.
// اختبارات_دقة_موقع_الخطأ_الشرطية_معلقة_للتشغيل_اليدوي.

// اختبار 12.1: خطأ بناء جملة في #إذا (عامل ثنائي مفقود)
// المتوقع: خطأ يشير إلى العمود بعد '+' أو عند '*'
#إذا 1 + * 2

// اختبار 12.2: خطأ بناء جملة في #إذا (قوس مفقود لـ 'معرف')
// المتوقع: خطأ يشير إلى العمود بعد '(' أو حيث كان يجب أن يكون المعرف
#إذا معرف(

// اختبار 12.3: خطأ بناء جملة في #إذا (تعبير غير مكتمل داخل الأقواس)
// المتوقع: خطأ يشير إلى العمود بعد '+' أو عند ')'
#إذا (VAL1 + )

// اختبار 12.4: خطأ بناء جملة في #إذا (عامل غير صالح)
// المتوقع: خطأ يشير إلى العمود عند '@'
#إذا VAL1 @ VAL2

// اختبار 12.5: خطأ بناء جملة في #وإلا_إذا
// المتوقع: خطأ يشير إلى العمود بعد '&&'
#تعريف ELIF_TEST 1
#إذا 0
  لا شيء
#وإلا_إذا ELIF_TEST && % 2
#نهاية_إذا


// --- 13. اختبار دقة موقع الخطأ في وسيطات التوجيه ---
// هذه الاختبارات يجب إلغاء تعليقها بشكل فردي للتحقق من موقع الخطأ.
// اختبارات_دقة_موقع_خطأ_وسيطات_التوجيه_معلقة_للتشغيل_اليدوي.

// اختبار 13.1: #تعريف بدون اسم
// المتوقع: خطأ يشير إلى العمود بعد "#تعريف "
#تعريف

// اختبار 13.2: #تعريف باسم ولكن بدون جسم
// المتوقع: (قد يكون مقبولاً كـ EMPTY_MACRO، أو خطأ إذا كان الجسم متوقعًا دائمًا)
// يعتمد على كيفية معالجة "الجسم المفقود". حاليًا يعامل كتعريف فارغ.
#تعريف MACRO_NO_BODY_TEST

// اختبار 13.3: #تعريف ماكرو وظيفي مع معامل غير صالح (رقم)
// المتوقع: خطأ يشير إلى العمود عند '1' في (1a)
#تعريف FUNC_BAD_PARAM_NUM(1a) 1a

// اختبار 13.4: #تعريف ماكرو وظيفي مع فاصلة مفقودة
// المتوقع: خطأ يشير إلى العمود عند 'b' في (a b)
#تعريف FUNC_MISSING_COMMA(a b) a+b

// اختبار 13.5: #تعريف ماكرو وظيفي مع فاصلة إضافية
// المتوقع: خطأ يشير إلى العمود عند ',,' أو عند 'b'
#تعريف FUNC_EXTRA_COMMA(a,,b) a+b

// اختبار 13.6: #تعريف ماكرو وظيفي بقوس إغلاق مفقود
// المتوقع: خطأ يشير إلى نهاية السطر أو حيث كان يجب أن يكون ')'
#تعريف FUNC_UNCLOSED_PARAM(a,b body

// اختبار 13.7: #تضمين بمسار غير منتهٍ (علامة اقتباس)
// المتوقع: خطأ يشير إلى نهاية السطر أو حيث كان يجب أن تكون علامة الاقتباس
#تضمين "unterminated_path

// اختبار 13.8: #تضمين بمسار غير منتهٍ (قوس زاوية)
// المتوقع: خطأ يشير إلى نهاية السطر أو حيث كان يجب أن يكون القوس
#تضمين <unterminated_angle

// اختبار 13.9: #تضمين بدون علامات اقتباس أو أقواس زاوية
// المتوقع: خطأ يشير إلى العمود عند 'no_quotes'
#تضمين no_quotes_or_brackets


// --- 14. اختبار دقة موقع الخطأ في استدعاء الماكرو ---
// هذه الاختبارات يجب إلغاء تعليقها بشكل فردي للتحقق من موقع الخطأ.
// اختبارات_دقة_موقع_خطأ_استدعاء_الماكرو_معلقة_للتشغيل_اليدوي.

#تعريف TWO_ARGS_MACRO(p1, p2) p1 + p2
#تعريف ONE_ARG_MACRO(p1) p1
#تعريف ZERO_ARGS_MACRO() EMPTY

// اختبار 14.1: وسيطات أقل من اللازم
// المتوقع: خطأ يشير إلى استدعاء TWO_ARGS_MACRO
TWO_ARGS_MACRO(arg1)

// اختبار 14.2: وسيطات أكثر من اللازم
// المتوقع: خطأ يشير إلى استدعاء TWO_ARGS_MACRO
TWO_ARGS_MACRO(arg1, arg2, arg3)

// اختبار 14.3: قوس غير مغلق في الوسيطات
// المتوقع: خطأ يشير إلى قرب "(unclosed_paren"
ONE_ARG_MACRO((unclosed_paren)

// اختبار 14.4: فاصلة مفقودة بين الوسيطات
// المتوقع: خطأ يشير إلى قرب "arg2"
TWO_ARGS_MACRO(arg1 arg2)

// اختبار 14.5: فاصلة إضافية في النهاية قبل القوس
// المتوقع: خطأ يشير إلى قرب ",)"
TWO_ARGS_MACRO(arg1, arg2,)

// اختبار 14.6: استدعاء ماكرو بصفر وسيطات مع وسيط
// المتوقع: خطأ يشير إلى استدعاء ZERO_ARGS_MACRO
ZERO_ARGS_MACRO(unexpected_arg)


// --- 15. اختبار #خطأ (Error Directive) ---
// يجب وضع هذا الاختبار في النهاية لأنه يوقف المعالجة.
// قم بإلغاء التعليق للتحقق من سلوك #خطأ.

#تعريف TRIGGER_ERROR_FINAL
#إذا_عرف TRIGGER_ERROR_FINAL
  #خطأ هذه رسالة خطأ اختبارية مقصودة في نهاية الملف.
  هذا السطر يجب ألا تتم معالجته أبدًا.
#نهاية_إذا

// إذا تم الوصول إلى هنا، فهذا يعني أن #خطأ لم يتم تفعيله (أو تم التعليق عليه)
تم إكمال جميع الاختبارات غير المسببة للخطأ بنجاح.
